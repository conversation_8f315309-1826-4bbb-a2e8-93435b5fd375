# Dynamic Context Length System

## Overview

Jaeger uses a **pure dynamic approach** to set context length for any LLM model loaded in LM Studio. The system automatically uses your configured context length (64K) when the model supports it, or falls back to the model's maximum supported context length.

## How It Works

### Simple Logic
1. **Try your configured context length** (64,000 tokens from `jaeger_config.env`)
2. **Query LM Studio** for the model's actual maximum supported context length
3. **Use the smaller value** between your request and model's maximum
4. **If model info unavailable**, use your configured value and let LM Studio handle any limits

### No Hardcoded Assumptions
- ✅ **No model databases** - queries LM Studio directly
- ✅ **No hardcoded limits** - uses real-time model information
- ✅ **Works with any model** - completely model-agnostic
- ✅ **Future-proof** - automatically adapts to new models

## Configuration

### Environment Settings
```env
# Your desired context length
LLM_CONTEXT_LENGTH=64000

# Enable/disable dynamic adjustment
LLM_AUTO_ADJUST_CONTEXT=true
```

### Behavior Control
- **`LLM_AUTO_ADJUST_CONTEXT=true`**: Use dynamic detection (recommended)
- **`LLM_AUTO_ADJUST_CONTEXT=false`**: Always use fixed 64K regardless of model

## Real-World Examples

### Model Supports Your Request
```
✅ Using requested 64,000 tokens (model supports it)
```
**Result**: Uses your full 64K request

### Model Has Lower Limit
```
📊 Model qwen2.5-coder-7b-instruct supports max 32,768 tokens
🎯 Using model maximum: 32,768 tokens
```
**Result**: Uses model's maximum (32K) instead of your 64K request

### Model Info Unavailable
```
⚠️ Could not query model info from LM Studio for unknown-model
🎯 Using requested 64,000 tokens (will let LM Studio handle limits)
```
**Result**: Uses your 64K request, LM Studio will handle any errors

## Implementation

### Core Method
```python
def get_optimal_context_length(self, model_id, requested_context_length):
    """Get optimal context length: use requested or model maximum, whichever is lower"""
    model_info = self.get_model_info(model_id)
    
    if model_info and model_info.get('max_context_length'):
        max_supported = model_info['max_context_length']
        return min(requested_context_length, max_supported)
    else:
        # Fallback: use requested value
        return requested_context_length
```

### API Integration
The system queries LM Studio's REST API endpoints:
- `/api/v0/models/{model_id}` - Get detailed model information
- `/v1/models` - Fallback for basic model list

## Benefits

1. **🎯 Optimal Performance**: Each model uses maximum supported context
2. **🛡️ Error Prevention**: Never exceeds model capabilities
3. **📊 Transparency**: Clear logging shows decisions made
4. **⚙️ Configurable**: Can disable if needed
5. **🔄 Future-Proof**: Works with any LM Studio model
6. **🧹 Clean**: No hardcoded assumptions or model databases

## Testing

The system has been tested with:
- ✅ Real models loaded in LM Studio
- ✅ Unknown/unavailable models
- ✅ Various context length scenarios
- ✅ Fallback behavior verification

All tests confirm the system works purely dynamically without any hardcoded model assumptions.
